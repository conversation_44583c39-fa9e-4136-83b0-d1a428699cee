from collections import deque
import threading
import os
import logging
import json
import time
from PyQt5.QtCore import QObject, pyqtSignal

MAX_EVENTS = 25

class LiveFeedManager(QObject):
    feed_updated = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.events = deque(maxlen=MAX_EVENTS)
        self.lock = threading.Lock()
        self.thumbnails_to_cleanup = []  # Track thumbnails for cleanup
        self.events_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "live_feed_events.json")
        self.thumbnails_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "live_feed_thumbnails")
        os.makedirs(self.thumbnails_dir, exist_ok=True)
        self.load_events_from_file()

    def add_event(self, event):
        with self.lock:
            # Check if we're at max capacity and need to cleanup old thumbnails
            if len(self.events) >= MAX_EVENTS:
                # Get the oldest event that will be removed
                oldest_event = self.events[-1]
                self._cleanup_event_thumbnail(oldest_event)

            # Add timestamp to event
            event["timestamp"] = time.time()

            # For upload_post events, preserve thumbnail in persistent directory
            if event.get("type") == "upload_post" and event.get("thumb"):
                event["thumb"] = self._preserve_thumbnail_permanently(event["thumb"])

            self.events.appendleft(event)
            self.save_events_to_file()
            self.feed_updated.emit(list(self.events))

    def update_event(self, event_id, updated_event):
        """Update an existing event by ID"""
        with self.lock:
            for i, event in enumerate(self.events):
                if event.get("id") == event_id:
                    # Preserve timestamp from original event
                    if "timestamp" not in updated_event and "timestamp" in event:
                        updated_event["timestamp"] = event["timestamp"]
                    self.events[i] = updated_event
                    self.save_events_to_file()
                    self.feed_updated.emit(list(self.events))
                    return True
            return False

    def register_thumbnail_for_cleanup(self, thumb_path):
        """Register a thumbnail path for cleanup when event expires"""
        if thumb_path:
            with self.lock:
                self.thumbnails_to_cleanup.append(thumb_path)

    def _cleanup_event_thumbnail(self, event):
        """Clean up thumbnail associated with an event"""
        if event and event.get("type") == "upload_post":
            thumb_path = event.get("thumb", "")
            if thumb_path and thumb_path in self.thumbnails_to_cleanup:
                try:
                    if os.path.exists(thumb_path):
                        os.remove(thumb_path)
                        logging.debug(f"Event thumbnail cleaned up: {thumb_path}")
                    self.thumbnails_to_cleanup.remove(thumb_path)
                except Exception as e:
                    logging.debug(f"Event thumbnail cleanup failed: {e}")

    def get_events(self):
        with self.lock:
            return list(self.events)

    def clear_events(self):
        with self.lock:
            # Cleanup all thumbnails when clearing events
            for event in self.events:
                self._cleanup_event_thumbnail(event)
            self.events.clear()
            self.thumbnails_to_cleanup.clear()
            self.save_events_to_file()
            self.feed_updated.emit(list(self.events))

    def is_empty(self):
        with self.lock:
            return len(self.events) == 0

    def _preserve_thumbnail_permanently(self, temp_thumb_path):
        """Copy thumbnail to persistent directory and return new path"""
        if not temp_thumb_path or not os.path.exists(temp_thumb_path):
            return ""

        try:
            import shutil
            # Create unique filename with timestamp
            timestamp = int(time.time() * 1000)  # milliseconds for uniqueness
            filename = f"thumb_{timestamp}_{os.path.basename(temp_thumb_path)}"
            persistent_path = os.path.join(self.thumbnails_dir, filename)

            shutil.copy2(temp_thumb_path, persistent_path)
            logging.debug(f"Thumbnail preserved permanently: {persistent_path}")
            return persistent_path
        except Exception as e:
            logging.error(f"Failed to preserve thumbnail permanently: {e}")
            return temp_thumb_path

    def save_events_to_file(self):
        """Save current events to JSON file"""
        try:
            events_data = {
                "events": list(self.events),
                "thumbnails_to_cleanup": self.thumbnails_to_cleanup,
                "saved_at": time.time()
            }
            with open(self.events_file, 'w', encoding='utf-8') as f:
                json.dump(events_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Failed to save events to file: {e}")

    def load_events_from_file(self):
        """Load events from JSON file on startup"""
        try:
            if not os.path.exists(self.events_file):
                return

            with open(self.events_file, 'r', encoding='utf-8') as f:
                events_data = json.load(f)

            saved_events = events_data.get("events", [])
            self.thumbnails_to_cleanup = events_data.get("thumbnails_to_cleanup", [])

            # Filter out events older than 24 hours
            current_time = time.time()
            valid_events = []

            for event in saved_events:
                event_time = event.get("timestamp", 0)
                if current_time - event_time < 24 * 3600:  # 24 hours
                    # Verify thumbnail still exists
                    if event.get("type") == "upload_post" and event.get("thumb"):
                        if not os.path.exists(event["thumb"]):
                            event["thumb"] = ""  # Clear missing thumbnail
                    valid_events.append(event)
                else:
                    # Clean up old thumbnail
                    if event.get("type") == "upload_post" and event.get("thumb"):
                        try:
                            if os.path.exists(event["thumb"]):
                                os.remove(event["thumb"])
                        except Exception as e:
                            logging.debug(f"Failed to cleanup old thumbnail: {e}")

            # Load valid events (newest first)
            self.events = deque(valid_events[:MAX_EVENTS], maxlen=MAX_EVENTS)

            logging.info(f"Loaded {len(self.events)} live feed events from file")

        except Exception as e:
            logging.error(f"Failed to load events from file: {e}")
            self.events = deque(maxlen=MAX_EVENTS)

    def cleanup_old_thumbnails(self):
        """Clean up thumbnails older than 24 hours"""
        try:
            current_time = time.time()
            for filename in os.listdir(self.thumbnails_dir):
                filepath = os.path.join(self.thumbnails_dir, filename)
                if os.path.isfile(filepath):
                    # Extract timestamp from filename
                    try:
                        if filename.startswith("thumb_"):
                            timestamp_str = filename.split("_")[1]
                            file_time = int(timestamp_str) / 1000  # Convert from milliseconds
                            if current_time - file_time > 24 * 3600:  # 24 hours
                                os.remove(filepath)
                                logging.debug(f"Cleaned up old thumbnail: {filepath}")
                    except (ValueError, IndexError):
                        # If we can't parse timestamp, check file modification time
                        file_time = os.path.getmtime(filepath)
                        if current_time - file_time > 24 * 3600:
                            os.remove(filepath)
                            logging.debug(f"Cleaned up old thumbnail by mtime: {filepath}")
        except Exception as e:
            logging.error(f"Failed to cleanup old thumbnails: {e}")

live_feed_manager = LiveFeedManager()