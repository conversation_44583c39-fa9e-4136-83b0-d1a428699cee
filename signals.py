from PyQt5.QtCore import QObject, pyqtSignal

class FeedSignalEmitter(QObject):
    add_item_to_feed_signal = pyqtSignal(dict)
    update_feed_item_signal = pyqtSignal(dict)

feed_emitter = FeedSignalEmitter()

# Live Feed entegrasyonu
from live_feed import live_feed_manager
feed_emitter.add_item_to_feed_signal.connect(live_feed_manager.add_event)

def update_feed_event(event_id, updated_event):
    """Update an existing feed event"""
    live_feed_manager.update_event(event_id, updated_event)